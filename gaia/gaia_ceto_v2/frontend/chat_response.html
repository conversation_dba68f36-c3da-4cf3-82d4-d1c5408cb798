<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ceto Chat Response</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Minimal assets for raw chat rendering test -->
    <link rel="stylesheet" type="text/css" href="../../djangaia/ceto_chat/static/ceto_chat/ceto_chat.css">

    <!-- Configuration for standalone mode -->
    <script>
      window.MCP_API_BASE = 'http://127.0.0.1:9000';
      window.CHAT_USERNAME = 'user'; // Fallback for standalone mode
    </script>

    <!-- Vue ESM via import map -->
    <script type="importmap">
    { "imports": { "vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js" } }
    </script>

    <style>
        /* Minimal tweaks for this page */
        .response-box { background:#f5f5f5; border:1px solid #ddd; border-radius:6px; padding:12px; white-space:pre-wrap; }
        .chat-input-row { display:flex; gap:10px; align-items:center; flex-wrap:wrap; }
        .chat-input-row input[type="text"] { flex: 1 1 420px; padding:8px; border:1px solid #ccc; border-radius:4px; }
        .message-item { margin:6px 0; }
        .message-item .role { font-weight:600; margin-right:6px; color:#555; }
        .badge-id { padding: 3px 8px; font-size: 12px; line-height: 1; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d; }
    </style>
</head>
<body>
<div id="app" class="app-container">
    <!-- Debug Panel Toggle Button -->
    <button @click="toggleDebugPanel"
            class="btn btn-sm debug-toggle"
            :class="debugPanelVisible ? 'btn-warning' : 'btn-outline-secondary'"
            style="position: fixed; top: 10px; right: 10px; z-index: 1050; background: #333; color: white; border: 1px solid #666;">
        <i class="fas fa-bug"></i> Debug
    </button>

    <div class="main-content">
      <h1 style="margin-top:0;">Raw Chat Rendering (Mock-only)</h1>

      <div style="display:flex; gap:10px; align-items:center; flex-wrap:wrap; margin:10px 0;">
        <input v-model="userText" type="text" placeholder="Type a message" style="padding:8px; width: 360px; border:1px solid #ccc; border-radius:3px;">
        <button @click="sendMock" :disabled="sending || !userText.trim()"
                style="padding:8px 16px; background:#007bff; color:#fff; border:none; border-radius:3px; cursor:pointer;"
                :style="{ opacity: (sending || !userText.trim()) ? 0.6 : 1 }">
          <span v-if="sending"><i class="fas fa-spinner fa-spin"></i> Sending…</span>
          <span v-else>Send</span>
        </button>
      </div>

      <chat-response-panel :messages="messages" :response="lastAssistantResponse"></chat-response-panel>
    </div>

    <!-- Debug Panel Component -->
    <debug-panel :show-tools-tab="false"></debug-panel>

</div>

<script type="module">
  import { createApp } from 'vue';
  import { DebugPanelMixin, DebugPanelComponent } from '../../djangaia/ceto_chat/static/ceto_chat/debug.js';
  import ChatResponsePanel from '../../djangaia/ceto_chat/static/ceto_chat/chat_response.module.js';

  const app = createApp({
    mixins: [DebugPanelMixin],
    data() {
      return { userText: '', sending: false, messages: [], lastAssistantResponse: '' };
    },
    methods: {
      async sendMock() {
        if (!this.userText.trim()) return;
        const text = this.userText.trim();
        this.userText = '';
        this.sending = true;
        try {
          this.messages.push({ role: 'user', content: text, timestamp: new Date().toISOString() });
          const reply = `Echo: ${text}`;
          this.messages.push({ role: 'assistant', content: reply, timestamp: new Date().toISOString(), mock_mode: true });
          this.lastAssistantResponse = reply;
        } finally { this.sending = false; }
      }
    }
  });

  app.component('chat-response-panel', ChatResponsePanel);
  app.component('debug-panel', DebugPanelComponent);

  app.mount('#app');
</script>

</body>
</html>
