<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Port System Test</title>
  <script>
    window.MCP_API_BASE = 'http://127.0.0.1:9000';
    window.CHAT_USERNAME = 'user'; // Fallback for standalone mode
  </script>
  
  <!-- Vue ESM via import map -->
  <script type="importmap">
  {
    "imports": {
      "vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js"
    }
  }
  </script>
</head>
<body>
  <div id="app">
    <h1>Port System Test</h1>
    <div id="status">Loading...</div>
    <div id="results"></div>
  </div>

  <script type="module">
    import { createAuto } from './adapters/index.js';
    import { useChat } from './core/app/useChat.js';

    async function testPorts() {
      const statusEl = document.getElementById('status');
      const resultsEl = document.getElementById('results');
      
      try {
        statusEl.textContent = 'Testing port system...';
        console.log('🧪 Starting port system test');
        
        // Test auto-detection
        const config = await createAuto({
          mcpApiBase: window.MCP_API_BASE || 'http://localhost:9000'
        });
        
        console.log('✅ Port configuration:', config);
        statusEl.textContent = `Port system working! Level: ${config.level}`;
        
        // Test chat kernel
        const chat = useChat(config.chatPort, config.renderPort, config.effectsPort);
        console.log('✅ Chat kernel created:', chat);
        
        // Test tool loading
        const toolsResult = await chat.loadTools();
        console.log('✅ Tools loaded:', toolsResult);
        
        resultsEl.innerHTML = `
          <h3>Results:</h3>
          <p><strong>Level:</strong> ${config.level}</p>
          <p><strong>Chat Port:</strong> ${config.chatPort.name}</p>
          <p><strong>Tools:</strong> ${toolsResult.tools?.length || 0}</p>
          <p><strong>Connected:</strong> ${toolsResult.connected || false}</p>
          <p><strong>Mock Mode:</strong> ${toolsResult.mockUsed || false}</p>
        `;
        
      } catch (error) {
        console.error('❌ Port system test failed:', error);
        statusEl.textContent = `Error: ${error.message}`;
        resultsEl.innerHTML = `<pre style="color: red;">${error.stack}</pre>`;
      }
    }
    
    // Run test when page loads
    testPorts();
  </script>
</body>
</html>
