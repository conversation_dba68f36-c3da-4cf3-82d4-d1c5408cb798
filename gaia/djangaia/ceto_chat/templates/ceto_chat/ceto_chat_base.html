{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title|default:"Gaia Chat" }}</title>

    <!-- Using Vue via ESM import map below; remove global build to avoid duplication -->
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Ceto Chat CSS -->
    <link rel="stylesheet" type="text/css" href="{% static 'ceto_chat/css/ceto_chat.css' %}">

    <!-- Enhanced styling for elegant and slick look -->
    <link rel="stylesheet" type="text/css" href="{% static 'ceto_chat/css/enhanced_style.css' %}">

    <!-- Configure MCP HTTP base so the frontend calls the MCP server directly -->
    <script>
      window.MCP_API_BASE = "{{ mcp_server_url|default:'http://localhost:9000' }}";
      {% if user.is_authenticated %}
      window.CHAT_USERNAME = "{{ user.username|escapejs }}";
      {% else %}
      window.CHAT_USERNAME = "user";
      {% endif %}
    </script>


    <!-- Import map for Vue ESM (can be pointed to a local copy later) -->
    <script type="importmap">
    {
      "imports": {
        "vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js"
      }
    }
    </script>

    <!-- ESM entrypoint -->
    <script type="module" src="{% static 'ceto_chat/ceto_app.module.js' %}"></script>
    <!-- Optional UI flourishes (spinner/busy) listening to ceto:chat:* events -->
    <script type="module" src="{% static 'ceto_chat/ui_flourishes.js' %}"></script>

    {% block head_extra %}{% endblock %}
</head>
<body>
<div id="app" class="app-container enhanced-theme">
    <!-- Debug Panel Toggle Button -->
    <button @click="toggleDebugPanel"
            class="btn btn-sm debug-toggle"
            :class="debugPanelVisible ? 'btn-warning' : 'btn-outline-secondary'"
            style="position: fixed; top: 10px; right: 10px; z-index: 1050; background: #333; color: white; border: 1px solid #666;">
        <i class="fas fa-bug"></i> Debug
    </button>

    <!-- Sidebar with AgFunder Gaia branding (lightweight subset) -->
    <div class="sidebar">
        <div class="logo">
            <img src="https://agfunder-gaia-assets.s3.us-west-2.amazonaws.com/agfunder_logo.svg" alt="AgFunder Logo">
        </div>
        <h2 style="margin-top: 6px;">Gaia Chat</h2>
        <div style="margin-bottom: 1rem">
            <img style="width:100px" src="https://agfunder-gaia-assets.s3.us-west-2.amazonaws.com/AI-in-agriculture-istock-Igor-Borisenko.jpg" width="100%" height="100%" alt="Gaia Chat Banner" style="border-radius: 2px">
        </div>
        {% if user.is_authenticated %}
        <div class="user-info">
            <p>Welcome, {{ user.username }}</p>
            <div class="user-links">
                <a href="{% url 'ceto_chat:profile' %}" class="user-link">Profile</a>
                <a href="{% url 'ceto_chat:logout' %}" class="user-link">Logout</a>
            </div>
        </div>
        {% else %}
        <div class="user-info">
            <div class="user-links">
                <a href="{% url 'ceto_chat:login' %}" class="user-link">Login</a>
                <a href="{% url 'ceto_chat:register' %}" class="user-link">Register</a>
            </div>
        </div>
        {% endif %}
        <div style="color:#666; font-size: 0.9rem; margin-top: .5rem;">
            ✓ Using MCP HTTP protocol
        </div>
        {% block sidebar_extra %}{% endblock %}
    </div>

    <div class="main-content">
        {% block main_content %}{% endblock %}
    </div>

    <!-- Debug Panel Component (Tools tab off for chat page) -->
    <debug-panel :show-tools-tab="false"></debug-panel>
</div>
</body>
</html>

