#!/usr/bin/env python3
"""
Simple test to verify that the username display functionality works correctly.
This test checks both Django template rendering and JavaScript functionality.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the Django project to the path
sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')

def test_django_template_rendering():
    """Test that the Django template correctly sets window.CHAT_USERNAME"""
    print("Testing Django template rendering...")
    
    # Set up minimal Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gaia.djangaia.ceto_chat.test_settings')
    
    import django
    from django.conf import settings
    from django.template import Template, Context
    from django.contrib.auth.models import User
    
    django.setup()
    
    # Read the template file
    template_path = Path('/home/<USER>/django-projects/agbase_admin/gaia/djangaia/ceto_chat/templates/ceto_chat/ceto_chat_base.html')
    template_content = template_path.read_text()
    
    # Create a template object
    template = Template(template_content)
    
    # Test with authenticated user
    user = User(username='testuser', is_authenticated=True)
    context = Context({
        'user': user,
        'mcp_server_url': 'http://localhost:9000'
    })
    
    rendered = template.render(context)
    
    # Check that the username is correctly set
    assert 'window.CHAT_USERNAME = "testuser";' in rendered, "Username not found in rendered template"
    print("✓ Authenticated user template renders correctly")
    
    # Test with unauthenticated user
    class AnonymousUser:
        is_authenticated = False
        username = None
    
    context = Context({
        'user': AnonymousUser(),
        'mcp_server_url': 'http://localhost:9000'
    })
    
    rendered = template.render(context)
    
    # Check that fallback is used
    assert 'window.CHAT_USERNAME = "user";' in rendered, "Fallback username not found in rendered template"
    print("✓ Unauthenticated user template renders correctly")

def test_javascript_functionality():
    """Test that the JavaScript correctly uses the username"""
    print("\nTesting JavaScript functionality...")
    
    # Read the JavaScript file
    js_path = Path('/home/<USER>/django-projects/agbase_admin/gaia/djangaia/ceto_chat/static/ceto_chat/core/ui/ChatWindow.module.js')
    js_content = js_path.read_text()
    
    # Check that the toChatItem function uses window.CHAT_USERNAME
    assert 'window.CHAT_USERNAME' in js_content, "window.CHAT_USERNAME not found in JavaScript"
    assert 'displayRole = (role === \'user\' && window.CHAT_USERNAME) ? window.CHAT_USERNAME : role;' in js_content, "Username logic not found in toChatItem"
    print("✓ JavaScript toChatItem function updated correctly")
    
    # Check that timeline processing also uses username
    assert 'const displayRole = (role === \'user\' && window.CHAT_USERNAME) ? window.CHAT_USERNAME : role;' in js_content, "Username logic not found in timeline processing"
    print("✓ JavaScript timeline processing updated correctly")

def test_standalone_files():
    """Test that standalone HTML files have the fallback username"""
    print("\nTesting standalone HTML files...")
    
    standalone_files = [
        '/home/<USER>/django-projects/agbase_admin/gaia/gaia_ceto_v2/frontend/chat_response_with_tools.html',
        '/home/<USER>/django-projects/agbase_admin/gaia/gaia_ceto_v2/frontend/chat_response.html',
        '/home/<USER>/django-projects/agbase_admin/gaia/djangaia/ceto_chat/static/ceto_chat/test_ports.html'
    ]
    
    for file_path in standalone_files:
        if Path(file_path).exists():
            content = Path(file_path).read_text()
            assert 'window.CHAT_USERNAME = \'user\';' in content, f"Fallback username not found in {file_path}"
            print(f"✓ {Path(file_path).name} has correct fallback username")

def main():
    """Run all tests"""
    print("Testing username display functionality...\n")
    
    try:
        test_django_template_rendering()
        test_javascript_functionality()
        test_standalone_files()
        
        print("\n🎉 All tests passed! Username display functionality is working correctly.")
        print("\nSummary of changes:")
        print("1. Django template now passes authenticated username or 'user' fallback to JavaScript")
        print("2. JavaScript toChatItem function uses username instead of 'user' for user messages")
        print("3. JavaScript timeline processing also uses username for user messages")
        print("4. Standalone HTML files have 'user' fallback for non-Django environments")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
